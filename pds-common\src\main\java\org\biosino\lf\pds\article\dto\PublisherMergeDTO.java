package org.biosino.lf.pds.article.dto;

import lombok.Data;

import java.util.List;

/**
 * 出版社合并参数
 */
@Data
public class PublisherMergeDTO {

    /**
     * 目标出版社ID（保留的出版社）
     */
    private Long targetId;

    /**
     * 源出版社ID列表（要合并的出版社）
     */
    private List<Long> sourceIds;

    /**
     * 合并后的出版社名称
     */
    private String name;

    /**
     * 合并后的出版社地址
     */
    private String ioc;

    /**
     * 合并后的别名
     */
    private List<String> alias;
}
