<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" class="search-form">
      <el-form-item label="出版社" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入出版社名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" class="search-input" placeholder="出版社状态" clearable>
          <el-option
            v-for="dict in statusOptions"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="更新时间">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="YYYY-MM-DD"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Sort"
          :disabled="multiple"
          @click="handleMerge"
        >批量合并</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="publisherList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="出版社名称" align="center" prop="name" show-overflow-tooltip/>
      <el-table-column label="出版社地址" align="center" prop="ioc" min-width="150" show-overflow-tooltip/>
      <el-table-column label="期刊数量" align="center" prop="journalCount" width="150">
        <template #default="scope">
          {{ scope.row.journalCount || 0 }}
        </template>
      </el-table-column>
      <el-table-column label="更新时间" align="center" prop="updateTime" width="250" />
      <el-table-column label="状态" align="center" prop="status" width="150">
        <template #default="scope">
          <el-tag :type="scope.row.status === '0' ? 'danger' : 'success'">
            {{ scope.row.status === '0' ? '停用' : '正常' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
        <template #default="scope">
          <el-button
            type="primary"
            link
            icon="Edit"
            @click="handleUpdate(scope.row)"
          >编辑</el-button>
          <el-button
            :type="scope.row.status === '0' ? 'success' : 'danger'"
            link
            icon="CircleCheck"
            v-if="scope.row.status === '0'"
            @click="handleStatusChange(scope.row)"
          >启用</el-button>
          <el-button
            :type="scope.row.status === '0' ? 'success' : 'danger'"
            link
            icon="CircleClose"
            v-else
            @click="handleStatusChange(scope.row)"
          >停用</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改出版社对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="publisherFormRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="出版社名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入出版社名称" />
        </el-form-item>
        <el-form-item label="出版社地址" prop="ioc">
          <el-input v-model="form.ioc" placeholder="请输入出版社地址" />
        </el-form-item>
        <el-form-item label="出版社别名" prop="aliasStr">
          <el-input
            v-model="form.aliasStr"
            type="textarea"
            placeholder="请输入出版社别名，每行一个"
            :rows="4"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in statusOptions"
              :key="dict.value"
              :label="dict.value"
            >{{ dict.label }}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 出版社合并对话框 -->
    <el-dialog title="出版社合并" v-model="mergeOpen" width="500px" append-to-body>
      <div v-if="selectedPublishers.length < 2" class="merge-warning">
        请至少选择两个出版社进行合并操作
      </div>
      <el-form v-else ref="mergeFormRef" :model="mergeData" label-width="120px">
        <el-form-item label="出版社名称" prop="name">
          <el-autocomplete
            v-model="mergeData.name"
            :fetch-suggestions="queryPublisherNames"
            placeholder="请输入或选择合并后的出版社名称"
            clearable
            style="width: 100%"
          >
            <template #default="{ item }">
              <div>{{ item.value }}</div>
            </template>
          </el-autocomplete>
        </el-form-item>
        <el-form-item label="出版社地址" prop="address">
          <el-autocomplete
            v-model="mergeData.address"
            :fetch-suggestions="queryPublisherAddresses"
            placeholder="请输入或选择合并后的出版社地址"
            clearable
            style="width: 100%"
          >
            <template #default="{ item }">
              <div>{{ item.value }}</div>
            </template>
          </el-autocomplete>
        </el-form-item>
        <el-form-item label="出版社别名" prop="aliasStr">
          <el-input
            v-model="mergeData.aliasStr"
            type="textarea"
            placeholder="请输入出版社别名，每行一个"
            :rows="4"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitMerge" :disabled="selectedPublishers.length < 2">确 定</el-button>
          <el-button @click="cancelMerge">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, getCurrentInstance, watch, toRefs } from 'vue'
import Pagination from '@/components/Pagination'
import RightToolbar from '@/components/RightToolbar'
import { listPublisher, getPublisher, updatePublisher, mergePublisher, changePublisherStatus } from '@/api/article/publisher'

const { proxy } = getCurrentInstance()

// 显示搜索条件
const showSearch = ref(true)
// 选中数组
const ids = ref([])
// 选中的出版社对象
const selectedPublishers = ref([])
// 非单个禁用
const single = ref(true)
// 非多个禁用
const multiple = ref(true)
// 遮罩层
const loading = ref(false)
// 总条数
const total = ref(0)
// 出版社表格数据
const publisherList = ref([])
// 弹出层标题
const title = ref("")
// 是否显示弹出层
const open = ref(false)
// 是否显示合并弹出层
const mergeOpen = ref(false)
// 日期范围
const dateRange = ref([])
// 状态数据字典
const statusOptions = [
  { value: "1", label: "正常" },
  { value: "0", label: "停用" }
]

// 数据定义
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    name: undefined,
    status: undefined
  },
  form: {
    id: undefined,
    name: undefined,
    ioc: undefined,
    status: "1",
    alias: [],
    aliasStr: undefined
  },
  mergeData: {
    name: undefined,
    address: undefined,
    alias: [],
    aliasStr: undefined
  }
})

const { queryParams, form, mergeData } = toRefs(data)

// 监听合并表单别名字符串变化，切割为数组
watch(() => mergeData.value.aliasStr, (newVal) => {
  if (newVal) {
    mergeData.value.alias = newVal.split('\n').filter(item => item.trim() !== '')
  } else {
    mergeData.value.alias = []
  }
})

// 监听编辑表单别名字符串变化，切割为数组
watch(() => form.value.aliasStr, (newVal) => {
  if (newVal) {
    form.value.alias = newVal.split('\n').filter(item => item.trim() !== '')
  } else {
    form.value.alias = []
  }
})

// 表单校验
const rules = reactive({
  name: [
    { required: true, message: "出版社名称不能为空", trigger: "blur" }
  ]
})

// 表单引用
const publisherFormRef = ref(null)
const mergeFormRef = ref(null)
const queryForm = ref(null)

// 出版社名称建议
function queryPublisherNames(queryString, cb) {
  const suggestions = selectedPublishers.value.map(publisher => ({
    value: publisher.name
  }));

  const results = queryString
    ? suggestions.filter(item => item.value.toLowerCase().includes(queryString.toLowerCase()))
    : suggestions;

  cb(results);
}

// 出版社地址建议
function queryPublisherAddresses(queryString, cb) {
  const suggestions = selectedPublishers.value.map(publisher => ({
    value: publisher.ioc
  }));

  const results = queryString
    ? suggestions.filter(item => item.value && item.value.toLowerCase().includes(queryString.toLowerCase()))
    : suggestions;

  cb(results);
}



/** 查询出版社列表 */
function getList() {
  loading.value = true
  listPublisher(proxy.addDateRange(queryParams.value, dateRange.value)).then(response => {
    publisherList.value = response.rows
    total.value = response.total
    loading.value = false
  }).catch(() => {
    loading.value = false
  })
}

/** 取消按钮 */
function cancel() {
  open.value = false
  resetForm()
}

/** 取消合并按钮 */
function cancelMerge() {
  mergeOpen.value = false
  mergeData.value.name = undefined
  mergeData.value.address = undefined
  mergeData.value.alias = []
  mergeData.value.aliasStr = undefined
}

/** 表单重置 */
function resetForm() {
  if (publisherFormRef.value) {
    publisherFormRef.value.resetFields()
  }
  form.value.id = undefined
  form.value.name = undefined
  form.value.ioc = undefined
  form.value.status = "1"
  form.value.alias = []
  form.value.aliasStr = undefined
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = []
  if (queryForm.value) {
    queryForm.value.resetFields()
  }
  queryParams.value.pageNum = 1
  handleQuery()
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id)
  selectedPublishers.value = selection
  single.value = selection.length !== 1
  multiple.value = !selection.length
}



/** 修改按钮操作 */
function handleUpdate(row) {
  resetForm()
  const publisherId = row.id || ids.value[0]
  getPublisher(publisherId).then(response => {
    Object.assign(form.value, response.data)
    // 将alias数组转换为字符串
    if (form.value.alias && Array.isArray(form.value.alias)) {
      form.value.aliasStr = form.value.alias.join('\n')
    }
    open.value = true
    title.value = "修改出版社"
  })
}

/** 提交按钮 */
function submitForm() {
  if (!publisherFormRef.value) return

  publisherFormRef.value.validate(valid => {
    if (valid) {
      updatePublisher(form.value).then(() => {
        proxy.$modal.msgSuccess("修改成功")
        open.value = false
        getList()
      })
    }
  })
}

/** 合并按钮操作 */
function handleMerge() {
  if (ids.value.length < 2) {
    proxy.$modal.msgWarning("请至少选择两个出版社进行合并操作")
    return
  }
  mergeOpen.value = true
  mergeData.value.name = undefined
  mergeData.value.address = undefined
  mergeData.value.alias = []
  mergeData.value.aliasStr = undefined

  // 默认使用第一个选中的出版社作为名称和地址的初始值
  if (selectedPublishers.value.length > 0) {
    mergeData.value.name = selectedPublishers.value[0].name
    mergeData.value.address = selectedPublishers.value[0].ioc
  }
}

/** 提交合并 */
function submitMerge() {
  if (!mergeData.value.name) {
    proxy.$modal.msgWarning("请输入合并后的出版社名称")
    return
  }

  // 默认将第一个选中的出版社作为目标出版社
  const targetPublisherId = selectedPublishers.value[0].id
  // 其余出版社作为源出版社
  const sourcePublisherIds = ids.value.filter(id => id !== targetPublisherId)

  const mergeParams = {
    targetId: targetPublisherId,
    sourceIds: sourcePublisherIds,
    name: mergeData.value.name,
    ioc: mergeData.value.address,
    alias: mergeData.value.alias
  }

  proxy.$modal.confirm('确认要将选中的出版社合并吗？合并后无法恢复', '警告').then(() => {
    mergePublisher(mergeParams).then(() => {
      proxy.$modal.msgSuccess("合并成功")
      mergeOpen.value = false
      getList()
    })
  }).catch(() => {})
}

/** 状态修改 */
function handleStatusChange(row) {
  const text = row.status === "0" ? "启用" : "停用"
  const newStatus = row.status === "0" ? 1 : 0

  proxy.$modal.confirm(`确认要${text}"${row.name}"出版社吗？`, '警告').then(() => {
    changePublisherStatus(row.id, newStatus).then(() => {
      proxy.$modal.msgSuccess(`${text}成功`)
      getList()
    })
  }).catch(() => {})
}

onMounted(() => {
  getList()
})
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.merge-warning {
  color: #f56c6c;
  text-align: center;
  margin: 20px 0;
}

.selected-publishers {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.publisher-tag {
  margin-right: 5px;
}

.mb8 {
  margin-bottom: 8px;
}

.search-form {
    .search-input {
      width: 200px;
    }
}
</style>
