package org.biosino.lf.pds.article.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.biosino.lf.pds.article.domain.Publisher;
import org.biosino.lf.pds.article.dto.PublisherMergeDTO;
import org.biosino.lf.pds.article.dto.PublisherQueryDTO;
import org.biosino.lf.pds.article.mapper.PublisherMapper;
import org.biosino.lf.pds.article.service.IPublisherService;
import org.biosino.lf.pds.common.exception.ServiceException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 出版社服务实现类
 */
@Service
@RequiredArgsConstructor
public class PublisherServiceImpl extends ServiceImpl<PublisherMapper, Publisher> implements IPublisherService {

    @Override
    public List<Publisher> selectPublisherList(PublisherQueryDTO queryDTO) {
        return this.baseMapper.selectPublisherList(queryDTO);
    }

    @Override
    public Publisher selectPublisherById(Long id) {
        return this.getById(id);
    }


    @Override
    public int updatePublisher(Publisher publisher) {
        if (publisher.getId() == null) {
            throw new ServiceException("出版社ID不能为空");
        }
        if (StrUtil.isBlank(publisher.getName())) {
            throw new ServiceException("出版社名称不能为空");
        }

        Publisher existPublisher = this.getById(publisher.getId());
        if (existPublisher == null) {
            throw new ServiceException("出版社不存在");
        }

        publisher.setUpdateTime(new Date());
        return this.updateById(publisher) ? 1 : 0;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public int mergePublishers(PublisherMergeDTO mergeDTO) {
        if (mergeDTO.getTargetId() == null) {
            throw new ServiceException("目标出版社ID不能为空");
        }
        if (mergeDTO.getSourceIds() == null || mergeDTO.getSourceIds().isEmpty()) {
            throw new ServiceException("源出版社ID不能为空");
        }
        if (StrUtil.isBlank(mergeDTO.getName())) {
            throw new ServiceException("合并后的出版社名称不能为空");
        }

        // 检查目标出版社是否存在
        Publisher targetPublisher = this.getById(mergeDTO.getTargetId());
        if (targetPublisher == null) {
            throw new ServiceException("目标出版社不存在");
        }

        // 检查源出版社是否存在
        List<Publisher> sourcePublishers = this.listByIds(mergeDTO.getSourceIds());
        if (sourcePublishers.size() != mergeDTO.getSourceIds().size()) {
            throw new ServiceException("部分源出版社不存在");
        }

        // 更新目标出版社信息
        targetPublisher.setName(mergeDTO.getName());
        if (StrUtil.isNotBlank(mergeDTO.getIoc())) {
            targetPublisher.setIoc(mergeDTO.getIoc());
        }
        if (CollUtil.isNotEmpty(mergeDTO.getAlias())) {
            targetPublisher.setAlias(mergeDTO.getAlias());
        }
        targetPublisher.setUpdateTime(new Date());

        this.updateById(targetPublisher);

        // 删除源出版社
        this.removeByIds(mergeDTO.getSourceIds());

        return 1;
    }

    @Override
    public int updatePublisherStatus(Long id, Integer status) {
        if (id == null) {
            throw new ServiceException("出版社ID不能为空");
        }
        if (status == null) {
            throw new ServiceException("状态不能为空");
        }

        Publisher publisher = this.getById(id);
        if (publisher == null) {
            throw new ServiceException("出版社不存在");
        }

        publisher.setStatus(status);
        publisher.setUpdateTime(new Date());

        return this.updateById(publisher) ? 1 : 0;
    }
}
