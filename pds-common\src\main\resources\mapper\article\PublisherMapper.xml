<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.biosino.lf.pds.article.mapper.PublisherMapper">

    <resultMap id="PublisherResult" type="org.biosino.lf.pds.article.domain.Publisher">
        <id property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="ioc" column="ioc"/>
        <result property="alias" column="alias"
                typeHandler="org.biosino.lf.pds.article.config.StringListArrayTypeHandler"/>
        <result property="sourceType" column="source_type"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectSql">
        select id,
               name,
               ioc,
               alias,
               source_type,
               status,
               create_time,
               update_time
        from tb_dds_publisher_250715
    </sql>

    <!-- 查询出版社列表 -->
    <select id="selectPublisherList" parameterType="org.biosino.lf.pds.article.dto.PublisherQueryDTO"
            resultMap="PublisherResult">
        <include refid="selectSql"/>
        <where>
            <if test="name != null and name != ''">
                and name like concat('%', #{name}, '%')
            </if>
            <if test="ioc != null and ioc != ''">
                and ioc like concat('%', #{ioc}, '%')
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
            <if test="sourceType != null and sourceType != ''">
                and source_type = #{sourceType}
            </if>
        </where>
        order by update_time desc
    </select>



</mapper>
