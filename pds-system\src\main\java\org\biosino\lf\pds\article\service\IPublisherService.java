package org.biosino.lf.pds.article.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.biosino.lf.pds.article.domain.Publisher;
import org.biosino.lf.pds.article.dto.PublisherMergeDTO;
import org.biosino.lf.pds.article.dto.PublisherQueryDTO;

import java.util.List;

/**
 * 出版社服务接口
 */
public interface IPublisherService extends IService<Publisher> {

    /**
     * 查询出版社列表
     *
     * @param queryDTO 查询条件
     * @return 出版社列表
     */
    List<Publisher> selectPublisherList(PublisherQueryDTO queryDTO);

    /**
     * 根据ID查询出版社
     *
     * @param id 出版社ID
     * @return 出版社
     */
    Publisher selectPublisherById(Long id);


    /**
     * 修改出版社
     *
     * @param publisher 出版社
     * @return 结果
     */
    int updatePublisher(Publisher publisher);


    /**
     * 合并出版社
     *
     * @param mergeDTO 合并参数
     * @return 结果
     */
    int mergePublishers(PublisherMergeDTO mergeDTO);

    /**
     * 修改出版社状态
     *
     * @param id     出版社ID
     * @param status 状态
     * @return 结果
     */
    int updatePublisherStatus(Long id, Integer status);
}
